/**
 * PageSpy生命周期管理器
 * 处理浏览器崩溃、刷新、tab关闭时的暂停和恢复功能
 */

// 状态存储键
const PAGESPY_STATE_KEY = 'pagespy-recording-state';
const PAGESPY_SESSION_KEY = 'pagespy-session-id';

interface PageSpyState {
  isPaused: boolean;
  timestamp: number;
  sessionId: string;
  reason?: 'manual' | 'beforeunload' | 'visibility' | 'pagehide';
}

interface PageSpyInstance {
  abort?: () => void;
  render?: () => void;
  [key: string]: any;
}

interface DataHarborInstance {
  onOfflineLog?: (action: 'upload' | 'download') => Promise<any> | void;
  [key: string]: any;
}

declare global {
  interface Window {
    $pageSpy?: PageSpyInstance;
    $harbor?: DataHarborInstance;
    PageSpy?: any;
    DataHarborPlugin?: any;
    RRWebPlugin?: any;
  }
}

class PageSpyLifecycleManager {
  private isInitialized = false;
  private currentSessionId: string;
  private originalConsoleLog: typeof console.log;
  private isRecordingPaused = false;

  constructor() {
    this.currentSessionId = this.generateSessionId();
    this.originalConsoleLog = console.log;
  }

  /**
   * 初始化生命周期管理
   */
  public initialize(): void {
    if (this.isInitialized) {
      console.warn('PageSpy生命周期管理器已经初始化');
      return;
    }

    console.log('初始化PageSpy生命周期管理器');
    
    // 检查上次会话状态
    this.checkPreviousSession();
    
    // 设置事件监听器
    this.setupEventListeners();
    
    // 保存当前会话
    this.saveCurrentSession();
    
    this.isInitialized = true;
  }

  /**
   * 检查上次会话状态
   */
  private checkPreviousSession(): void {
    const previousState = this.getRecordingState();
    const previousSession = localStorage.getItem(PAGESPY_SESSION_KEY);
    
    if (previousState.isPaused && previousSession !== this.currentSessionId) {
      console.log('检测到上次会话被暂停，恢复录制');
      this.resumeRecording('session-restore');
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 页面卸载前（刷新、关闭）
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    
    // 页面可见性变化（tab切换）
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    
    // 页面隐藏（更可靠的页面关闭检测）
    window.addEventListener('pagehide', this.handlePageHide.bind(this));
    
    // 页面显示（从后台恢复）
    window.addEventListener('pageshow', this.handlePageShow.bind(this));
    
    // 窗口焦点变化
    window.addEventListener('focus', this.handleWindowFocus.bind(this));
    window.addEventListener('blur', this.handleWindowBlur.bind(this));
  }

  /**
   * 处理页面卸载前事件
   */
  private handleBeforeUnload(event: BeforeUnloadEvent): void {
    console.log('页面即将卸载，暂停PageSpy录制');
    this.pauseRecording('beforeunload');
    
    // 尝试上传离线数据（如果支持）
    this.tryUploadOfflineData();
  }

  /**
   * 处理页面可见性变化
   */
  private handleVisibilityChange(): void {
    if (document.hidden) {
      console.log('页面变为隐藏状态，暂停PageSpy录制');
      this.pauseRecording('visibility');
    } else {
      console.log('页面变为可见状态，恢复PageSpy录制');
      this.resumeRecording('visibility');
    }
  }

  /**
   * 处理页面隐藏事件
   */
  private handlePageHide(event: PageTransitionEvent): void {
    console.log('页面隐藏事件触发，暂停PageSpy录制');
    this.pauseRecording('pagehide');
  }

  /**
   * 处理页面显示事件
   */
  private handlePageShow(event: PageTransitionEvent): void {
    if (event.persisted) {
      console.log('页面从缓存恢复，恢复PageSpy录制');
      this.resumeRecording('pageshow');
    }
  }

  /**
   * 处理窗口获得焦点
   */
  private handleWindowFocus(): void {
    // 延迟恢复，避免频繁切换
    setTimeout(() => {
      if (!document.hidden) {
        this.resumeRecording('focus');
      }
    }, 100);
  }

  /**
   * 处理窗口失去焦点
   */
  private handleWindowBlur(): void {
    // 短暂延迟，避免误触发
    setTimeout(() => {
      if (document.hidden) {
        this.pauseRecording('blur');
      }
    }, 500);
  }

  /**
   * 暂停录制
   */
  public pauseRecording(reason: PageSpyState['reason'] = 'manual'): void {
    if (this.isRecordingPaused) {
      return;
    }

    console.log(`暂停PageSpy录制，原因: ${reason}`);
    this.isRecordingPaused = true;

    // 保存暂停状态
    this.saveRecordingState(true, reason);

    // 尝试暂停数据收集
    this.pauseDataCollection();
  }

  /**
   * 恢复录制
   */
  public resumeRecording(reason: string = 'manual'): void {
    if (!this.isRecordingPaused) {
      return;
    }

    console.log(`恢复PageSpy录制，原因: ${reason}`);
    this.isRecordingPaused = false;

    // 保存恢复状态
    this.saveRecordingState(false);

    // 恢复数据收集
    this.resumeDataCollection();
  }

  /**
   * 暂停数据收集
   */
  private pauseDataCollection(): void {
    // 由于DataHarborPlugin没有pause方法，我们采用其他策略
    // 1. 临时禁用console.log等数据收集
    // 2. 或者标记状态，在数据发送前检查
    
    // 暂时禁用console输出到PageSpy
    if (typeof console !== 'undefined') {
      // 这里可以实现更复杂的逻辑来暂停特定的数据收集
      console.log('数据收集已暂停');
    }
  }

  /**
   * 恢复数据收集
   */
  private resumeDataCollection(): void {
    // 恢复数据收集
    console.log('数据收集已恢复');
  }

  /**
   * 尝试上传离线数据
   */
  private tryUploadOfflineData(): void {
    try {
      if (window.$harbor && typeof window.$harbor.onOfflineLog === 'function') {
        console.log('尝试上传离线数据');
        window.$harbor.onOfflineLog('upload').catch((error: any) => {
          console.warn('上传离线数据失败:', error);
        });
      }
    } catch (error) {
      console.warn('上传离线数据时出错:', error);
    }
  }

  /**
   * 保存录制状态
   */
  private saveRecordingState(isPaused: boolean, reason?: PageSpyState['reason']): void {
    const state: PageSpyState = {
      isPaused,
      timestamp: Date.now(),
      sessionId: this.currentSessionId,
      reason
    };
    
    localStorage.setItem(PAGESPY_STATE_KEY, JSON.stringify(state));
  }

  /**
   * 获取录制状态
   */
  private getRecordingState(): PageSpyState {
    try {
      const stateStr = localStorage.getItem(PAGESPY_STATE_KEY);
      if (stateStr) {
        return JSON.parse(stateStr);
      }
    } catch (error) {
      console.warn('解析PageSpy状态失败:', error);
    }
    
    return {
      isPaused: false,
      timestamp: Date.now(),
      sessionId: this.currentSessionId
    };
  }

  /**
   * 保存当前会话ID
   */
  private saveCurrentSession(): void {
    localStorage.setItem(PAGESPY_SESSION_KEY, this.currentSessionId);
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `pagespy-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取当前状态
   */
  public getCurrentState(): { isPaused: boolean; sessionId: string } {
    return {
      isPaused: this.isRecordingPaused,
      sessionId: this.currentSessionId
    };
  }

  /**
   * 手动切换录制状态
   */
  public toggleRecording(): boolean {
    if (this.isRecordingPaused) {
      this.resumeRecording('manual');
    } else {
      this.pauseRecording('manual');
    }
    return this.isRecordingPaused;
  }

  /**
   * 清理资源
   */
  public destroy(): void {
    if (!this.isInitialized) {
      return;
    }

    console.log('销毁PageSpy生命周期管理器');
    
    // 移除事件监听器
    window.removeEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    window.removeEventListener('pagehide', this.handlePageHide.bind(this));
    window.removeEventListener('pageshow', this.handlePageShow.bind(this));
    window.removeEventListener('focus', this.handleWindowFocus.bind(this));
    window.removeEventListener('blur', this.handleWindowBlur.bind(this));
    
    this.isInitialized = false;
  }
}

// 创建全局实例
const pageSpyLifecycleManager = new PageSpyLifecycleManager();

// 导出管理器和便捷函数
export default pageSpyLifecycleManager;

export const setupPageSpyLifecycleManagement = () => {
  pageSpyLifecycleManager.initialize();
};

export const pausePageSpyRecording = () => {
  pageSpyLifecycleManager.pauseRecording('manual');
};

export const resumePageSpyRecording = () => {
  pageSpyLifecycleManager.resumeRecording('manual');
};

export const togglePageSpyRecording = () => {
  return pageSpyLifecycleManager.toggleRecording();
};

export const getPageSpyRecordingState = () => {
  return pageSpyLifecycleManager.getCurrentState();
};
