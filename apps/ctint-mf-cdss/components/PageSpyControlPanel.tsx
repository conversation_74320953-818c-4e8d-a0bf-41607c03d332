import React, { useState, useEffect } from 'react';
import {
  pausePageSpyRecording,
  resumePageSpyRecording,
  togglePageSpyRecording,
  getPageSpyRecordingState
} from '../utils/pagespy-lifecycle-manager';

interface PageSpyControlPanelProps {
  className?: string;
  showSessionInfo?: boolean;
}

const PageSpyControlPanel: React.FC<PageSpyControlPanelProps> = ({
  className = '',
  showSessionInfo = true
}) => {
  const [isPaused, setIsPaused] = useState(false);
  const [sessionId, setSessionId] = useState('');
  const [isVisible, setIsVisible] = useState(false);
  const [dataHarborMethods, setDataHarborMethods] = useState({
    pauseAvailable: false,
    resumeAvailable: false,
    uploadAvailable: false,
    downloadAvailable: false
  });

  useEffect(() => {
    // 初始化状态
    updateState();
    
    // 定期更新状态（以防外部改变）
    const interval = setInterval(updateState, 1000);
    
    return () => clearInterval(interval);
  }, []);

  const updateState = () => {
    try {
      const state = getPageSpyRecordingState();
      setIsPaused(state.isPaused);
      setSessionId(state.sessionId);
      setDataHarborMethods(state.dataHarborMethods);
    } catch (error) {
      console.warn('获取PageSpy状态失败:', error);
    }
  };

  const handleToggleRecording = () => {
    try {
      const newState = togglePageSpyRecording();
      setIsPaused(newState);
      console.log(`PageSpy录制已${newState ? '暂停' : '恢复'}`);
    } catch (error) {
      console.error('切换PageSpy录制状态失败:', error);
    }
  };

  const handlePauseRecording = () => {
    try {
      pausePageSpyRecording();
      setIsPaused(true);
      console.log('PageSpy录制已暂停');
    } catch (error) {
      console.error('暂停PageSpy录制失败:', error);
    }
  };

  const handleResumeRecording = () => {
    try {
      resumePageSpyRecording();
      setIsPaused(false);
      console.log('PageSpy录制已恢复');
    } catch (error) {
      console.error('恢复PageSpy录制失败:', error);
    }
  };

  const handleDownloadLogs = async () => {
    try {
      if (window.$harbor && typeof window.$harbor.onOfflineLog === 'function') {
        console.log('开始下载离线日志');
        await window.$harbor.onOfflineLog('download');
        console.log('离线日志下载完成');
      } else {
        console.warn('DataHarborPlugin不可用');
      }
    } catch (error) {
      console.error('下载离线日志失败:', error);
    }
  };

  const handleUploadLogs = async () => {
    try {
      if (window.$harbor && typeof window.$harbor.onOfflineLog === 'function') {
        console.log('开始上传离线日志');
        const result = await window.$harbor.onOfflineLog('upload');
        console.log('离线日志上传完成:', result);
      } else {
        console.warn('DataHarborPlugin不可用');
      }
    } catch (error) {
      console.error('上传离线日志失败:', error);
    }
  };

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  return (
    <div className={`pagespy-control-panel ${className}`}>
      {/* 切换按钮 */}
      <button
        onClick={toggleVisibility}
        className="pagespy-toggle-btn"
        style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          zIndex: 9999,
          padding: '8px 12px',
          backgroundColor: isPaused ? '#ff4d4f' : '#52c41a',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '12px',
          fontWeight: 'bold',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
        }}
        title={`PageSpy录制状态: ${isPaused ? '已暂停' : '录制中'}`}
      >
        📊 {isPaused ? '暂停' : '录制'}
      </button>

      {/* 控制面板 */}
      {isVisible && (
        <div
          className="pagespy-control-panel-content"
          style={{
            position: 'fixed',
            top: '50px',
            right: '10px',
            zIndex: 9998,
            backgroundColor: 'white',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            padding: '16px',
            minWidth: '280px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            fontSize: '14px'
          }}
        >
          <div style={{ marginBottom: '12px', fontWeight: 'bold', color: '#1890ff' }}>
            PageSpy 控制面板
          </div>

          {/* 状态显示 */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ marginBottom: '4px' }}>
              <span style={{ fontWeight: 'bold' }}>录制状态: </span>
              <span style={{ color: isPaused ? '#ff4d4f' : '#52c41a' }}>
                {isPaused ? '已暂停' : '录制中'}
              </span>
            </div>
            {showSessionInfo && (
              <div style={{ fontSize: '12px', color: '#666' }}>
                <div>会话ID: {sessionId.slice(-8)}...</div>
                <div style={{ marginTop: '4px' }}>
                  DataHarbor方法:
                  <span style={{ color: dataHarborMethods.pauseAvailable ? '#52c41a' : '#ff4d4f' }}>
                    {dataHarborMethods.pauseAvailable ? ' ✓' : ' ✗'} pause
                  </span>
                  <span style={{ color: dataHarborMethods.resumeAvailable ? '#52c41a' : '#ff4d4f' }}>
                    {dataHarborMethods.resumeAvailable ? ' ✓' : ' ✗'} resume
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* 控制按钮 */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <div style={{ display: 'flex', gap: '8px' }}>
              <button
                onClick={handlePauseRecording}
                disabled={isPaused}
                style={{
                  flex: 1,
                  padding: '6px 12px',
                  backgroundColor: isPaused ? '#f5f5f5' : '#ff4d4f',
                  color: isPaused ? '#999' : 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: isPaused ? 'not-allowed' : 'pointer',
                  fontSize: '12px'
                }}
              >
                暂停录制
              </button>
              <button
                onClick={handleResumeRecording}
                disabled={!isPaused}
                style={{
                  flex: 1,
                  padding: '6px 12px',
                  backgroundColor: !isPaused ? '#f5f5f5' : '#52c41a',
                  color: !isPaused ? '#999' : 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: !isPaused ? 'not-allowed' : 'pointer',
                  fontSize: '12px'
                }}
              >
                恢复录制
              </button>
            </div>

            <button
              onClick={handleToggleRecording}
              style={{
                padding: '8px 12px',
                backgroundColor: '#1890ff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px',
                fontWeight: 'bold'
              }}
            >
              {isPaused ? '🎬 开始录制' : '⏸️ 暂停录制'}
            </button>

            <div style={{ borderTop: '1px solid #f0f0f0', paddingTop: '8px', marginTop: '8px' }}>
              <div style={{ marginBottom: '8px', fontSize: '12px', color: '#666' }}>
                离线日志操作:
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                <button
                  onClick={handleDownloadLogs}
                  style={{
                    flex: 1,
                    padding: '6px 12px',
                    backgroundColor: '#722ed1',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  📥 下载日志
                </button>
                <button
                  onClick={handleUploadLogs}
                  style={{
                    flex: 1,
                    padding: '6px 12px',
                    backgroundColor: '#fa8c16',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  📤 上传日志
                </button>
              </div>
            </div>
          </div>

          {/* 说明文字 */}
          <div style={{ 
            marginTop: '12px', 
            fontSize: '11px', 
            color: '#999',
            borderTop: '1px solid #f0f0f0',
            paddingTop: '8px'
          }}>
            💡 系统会在页面刷新、tab切换或浏览器关闭时自动暂停录制，并在恢复时自动继续录制。
          </div>
        </div>
      )}
    </div>
  );
};

export default PageSpyControlPanel;
