# PageSpy 离线日志生命周期管理

## 概述

本功能实现了PageSpy离线日志在浏览器崩溃、刷新或关闭tab时的自动暂停和恢复功能，使用DataHarborPlugin的`pause()`和`resume()`方法来控制数据收集。

## 功能特性

### 🔄 自动生命周期管理
- **页面刷新时**：自动暂停录制，重新加载后自动恢复
- **Tab切换时**：页面隐藏时暂停录制，重新显示时恢复录制
- **浏览器关闭时**：自动暂停录制并尝试上传离线数据
- **窗口焦点变化**：失去焦点时暂停，获得焦点时恢复

### 🎛️ 手动控制
- 提供可视化控制面板
- 支持手动暂停/恢复录制
- 显示当前录制状态和会话信息
- 支持离线日志的上传和下载

### 💾 状态持久化
- 使用localStorage保存暂停/恢复状态
- 跨页面刷新保持状态一致性
- 会话ID管理，避免状态冲突

## 文件结构

```
apps/ctint-mf-cdss/
├── utils/
│   └── pagespy-lifecycle-manager.ts    # 核心生命周期管理器
├── components/
│   └── PageSpyControlPanel.tsx         # 可视化控制面板
├── pages/
│   ├── load-page-spy.tsx              # PageSpy配置（已修改）
│   └── pagespy-test.tsx               # 测试页面
└── docs/
    └── pagespy-lifecycle-management.md # 本文档
```

## 使用方法

### 1. 基本集成

PageSpy配置文件已经自动集成了生命周期管理：

```typescript
// apps/ctint-mf-cdss/pages/load-page-spy.tsx
import { setupPageSpyLifecycleManagement } from '../utils/pagespy-lifecycle-manager';

// PageSpy初始化后自动调用
setupPageSpyLifecycleManagement();
```

### 2. 添加控制面板（可选）

在需要显示控制面板的页面中：

```typescript
import PageSpyControlPanel from '../components/PageSpyControlPanel';

function MyPage() {
  return (
    <div>
      {/* 其他内容 */}
      <PageSpyControlPanel showSessionInfo={true} />
    </div>
  );
}
```

### 3. 手动控制API

```typescript
import {
  pausePageSpyRecording,
  resumePageSpyRecording,
  togglePageSpyRecording,
  getPageSpyRecordingState
} from '../utils/pagespy-lifecycle-manager';

// 手动暂停录制
pausePageSpyRecording();

// 手动恢复录制
resumePageSpyRecording();

// 切换录制状态
const isPaused = togglePageSpyRecording();

// 获取当前状态
const state = getPageSpyRecordingState();
console.log('录制状态:', state.isPaused);
console.log('会话ID:', state.sessionId);
console.log('DataHarbor方法可用性:', state.dataHarborMethods);
```

## DataHarborPlugin方法支持

### 主要方法
- `pause()`: 暂停数据收集
- `resume()`: 恢复数据收集
- `onOfflineLog('upload')`: 上传离线日志
- `onOfflineLog('download')`: 下载离线日志

### 兼容性处理
如果DataHarborPlugin的`pause()`和`resume()`方法不可用，系统会：
1. 使用备用方案（全局状态标记）
2. 在控制面板中显示方法可用性状态
3. 记录警告信息到控制台

## 事件监听

系统监听以下浏览器事件：

| 事件 | 触发条件 | 行为 |
|------|----------|------|
| `beforeunload` | 页面刷新/关闭前 | 暂停录制，尝试上传数据 |
| `visibilitychange` | Tab切换 | 隐藏时暂停，显示时恢复 |
| `pagehide` | 页面隐藏 | 暂停录制 |
| `pageshow` | 页面显示（从缓存） | 恢复录制 |
| `focus` | 窗口获得焦点 | 延迟恢复录制 |
| `blur` | 窗口失去焦点 | 延迟暂停录制 |

## 状态存储

### localStorage键值
- `pagespy-recording-state`: 录制状态信息
- `pagespy-session-id`: 当前会话ID

### 状态结构
```typescript
interface PageSpyState {
  isPaused: boolean;           // 是否暂停
  timestamp: number;           // 时间戳
  sessionId: string;           // 会话ID
  reason?: string;             // 暂停原因
}
```

## 测试

### 访问测试页面
```
http://localhost:4400/ctint/mf-cdss/pagespy-test
```

### 测试步骤
1. **检查控制面板状态**：确认DataHarbor方法可用性
2. **测试手动控制**：使用暂停/恢复按钮
3. **测试页面刷新**：验证状态持久化
4. **测试Tab切换**：验证可见性变化处理
5. **测试数据生成**：生成各种类型的测试数据
6. **测试离线日志**：上传/下载功能

## 调试

### 控制台输出
系统会输出详细的调试信息：
```
初始化PageSpy生命周期管理器
页面即将卸载，暂停PageSpy录制
调用DataHarborPlugin.pause()暂停数据收集
页面变为可见状态，恢复PageSpy录制
调用DataHarborPlugin.resume()恢复数据收集
```

### 状态检查
```javascript
// 在浏览器控制台中检查状态
console.log('PageSpy实例:', window.$pageSpy);
console.log('DataHarbor实例:', window.$harbor);
console.log('录制状态:', localStorage.getItem('pagespy-recording-state'));
```

## 注意事项

1. **方法可用性**：确保使用的PageSpy版本支持DataHarborPlugin的pause/resume方法
2. **性能影响**：频繁的暂停/恢复可能影响性能，系统已加入防抖处理
3. **数据完整性**：暂停期间的数据不会被收集，恢复后重新开始
4. **浏览器兼容性**：某些旧版浏览器可能不支持所有生命周期事件
5. **离线模式**：确保PageSpy配置为离线模式（`offline: true`）

## 故障排除

### 常见问题

**Q: DataHarbor方法显示不可用**
A: 检查PageSpy版本，确保DataHarborPlugin正确加载

**Q: 页面刷新后状态没有恢复**
A: 检查localStorage是否被清除，确保会话ID正确

**Q: 控制面板不显示**
A: 确保正确导入并使用PageSpyControlPanel组件

**Q: 暂停/恢复没有效果**
A: 检查控制台错误信息，确认DataHarborPlugin实例可用

### 调试命令
```javascript
// 检查管理器状态
window.__pageSpyLifecycleManager__ = pageSpyLifecycleManager;

// 手动触发事件
document.dispatchEvent(new Event('visibilitychange'));
window.dispatchEvent(new Event('beforeunload'));
```

## 更新日志

- **v1.0.0**: 初始版本，支持基本的暂停/恢复功能
- **v1.1.0**: 添加可视化控制面板
- **v1.2.0**: 增强兼容性处理和错误恢复
- **v1.3.0**: 添加测试页面和详细文档
