import Script from 'next/script';
import { setupPageSpyLifecycleManagement } from '../utils/pagespy-lifecycle-manager';

export default function LoadPageSpy() {
  console.log('LoadPageSpy');
  const baseUrl = `${typeof window !== 'undefined' ? window.location.protocol : 'http:'}//ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com`;

  return (
    <>
      <Script
        // 使用第二步：引入 SDK 文件
        src={`${baseUrl}/tracking/page-spy/index.min.js`}
        strategy="afterInteractive"
        onLoad={() => {
          // 使用第三步：实例化 PageSpy（参数都是可选的）
          // 使用第四步：在 app/page.tsx 中引入该组件
          // 之后即可使用 PageSpy，前往 https://pagespy.jikejishu.com 体验
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          window.$harbor = new DataHarborPlugin();
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          window.$rrweb = new RRWebPlugin();
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          [window.$harbor, window.$rrweb].forEach((p) => {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            PageSpy.registerPlugin(p);
          });

          // 实例化的参数都是可选的
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          window.$pageSpy = new PageSpy({
            api: 'ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com',
            clientOrigin: `${baseUrl}/tracking`,
            project: 'CDSS',
            logo: `${baseUrl}/ctint/mf-cdss/images/cdss-logo.svg`,
            autoRender: true,
            offline: true,
            title: localStorage.getItem('cdss-gc-username') || 'CDSS Debugger',
          });

          // 初始化PageSpy生命周期管理
          console.log('初始化PageSpy生命周期管理');
          setupPageSpyLifecycleManagement();
        }}
      />
    </>
  );
}
