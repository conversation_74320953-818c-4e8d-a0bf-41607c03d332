import React, { useEffect, useState } from 'react';
import PageSpyControlPanel from '../components/PageSpyControlPanel';
import LoadPageSpy from './load-page-spy';

const PageSpyTestPage: React.FC = () => {
  const [testLogs, setTestLogs] = useState<string[]>([]);

  useEffect(() => {
    // 添加一些测试日志
    const interval = setInterval(() => {
      const timestamp = new Date().toLocaleTimeString();
      const logMessage = `测试日志 - ${timestamp}`;
      console.log(logMessage);
      setTestLogs(prev => [...prev.slice(-9), logMessage]); // 保持最新10条
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const generateTestError = () => {
    console.error('这是一个测试错误', new Error('测试错误'));
  };

  const generateTestNetwork = async () => {
    try {
      const response = await fetch('/api/test-endpoint', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: 'data' })
      });
      console.log('网络请求测试完成');
    } catch (error) {
      console.log('网络请求测试（预期失败）:', error);
    }
  };

  const testLocalStorage = () => {
    const testKey = 'pagespy-test-' + Date.now();
    const testValue = { message: '测试数据', timestamp: Date.now() };
    localStorage.setItem(testKey, JSON.stringify(testValue));
    console.log('LocalStorage测试数据已保存:', testKey);
  };

  const simulatePageRefresh = () => {
    if (confirm('确定要刷新页面来测试PageSpy暂停/恢复功能吗？')) {
      window.location.reload();
    }
  };

  const simulateTabSwitch = () => {
    alert('请切换到其他tab页，然后再切换回来测试可见性变化功能');
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      {/* 加载PageSpy */}
      <LoadPageSpy />
      
      {/* 控制面板 */}
      <PageSpyControlPanel showSessionInfo={true} />

      <h1>PageSpy 离线日志暂停/恢复功能测试页面</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>功能说明</h2>
        <ul>
          <li>✅ 页面刷新时自动暂停录制，重新加载后恢复</li>
          <li>✅ Tab切换时自动暂停录制，回到页面时恢复</li>
          <li>✅ 浏览器关闭时自动暂停录制</li>
          <li>✅ 手动控制暂停/恢复功能</li>
          <li>✅ 离线日志上传/下载功能</li>
        </ul>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>测试操作</h2>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px', marginBottom: '10px' }}>
          <button 
            onClick={generateTestError}
            style={{ padding: '8px 16px', backgroundColor: '#ff4d4f', color: 'white', border: 'none', borderRadius: '4px' }}
          >
            生成测试错误
          </button>
          <button 
            onClick={generateTestNetwork}
            style={{ padding: '8px 16px', backgroundColor: '#1890ff', color: 'white', border: 'none', borderRadius: '4px' }}
          >
            测试网络请求
          </button>
          <button 
            onClick={testLocalStorage}
            style={{ padding: '8px 16px', backgroundColor: '#52c41a', color: 'white', border: 'none', borderRadius: '4px' }}
          >
            测试LocalStorage
          </button>
        </div>
        
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
          <button 
            onClick={simulatePageRefresh}
            style={{ padding: '8px 16px', backgroundColor: '#fa8c16', color: 'white', border: 'none', borderRadius: '4px' }}
          >
            🔄 测试页面刷新
          </button>
          <button 
            onClick={simulateTabSwitch}
            style={{ padding: '8px 16px', backgroundColor: '#722ed1', color: 'white', border: 'none', borderRadius: '4px' }}
          >
            🔄 测试Tab切换
          </button>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>实时测试日志</h2>
        <div style={{ 
          backgroundColor: '#f5f5f5', 
          padding: '10px', 
          borderRadius: '4px', 
          maxHeight: '200px', 
          overflowY: 'auto',
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          {testLogs.map((log, index) => (
            <div key={index} style={{ marginBottom: '2px' }}>
              {log}
            </div>
          ))}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>测试步骤</h2>
        <ol>
          <li>
            <strong>检查控制面板状态：</strong>
            <p>查看右上角的PageSpy控制面板，确认DataHarbor方法是否可用（显示✓表示可用）</p>
          </li>
          <li>
            <strong>测试手动暂停/恢复：</strong>
            <p>点击控制面板中的"暂停录制"和"恢复录制"按钮，观察状态变化</p>
          </li>
          <li>
            <strong>测试页面刷新：</strong>
            <p>点击"测试页面刷新"按钮，页面刷新后检查录制状态是否正确恢复</p>
          </li>
          <li>
            <strong>测试Tab切换：</strong>
            <p>切换到其他tab页，然后切换回来，观察录制状态变化</p>
          </li>
          <li>
            <strong>测试数据生成：</strong>
            <p>点击各种测试按钮生成不同类型的日志数据</p>
          </li>
          <li>
            <strong>测试离线日志：</strong>
            <p>使用控制面板中的"下载日志"和"上传日志"功能</p>
          </li>
        </ol>
      </div>

      <div style={{ 
        backgroundColor: '#e6f7ff', 
        border: '1px solid #91d5ff', 
        borderRadius: '4px', 
        padding: '12px',
        marginTop: '20px'
      }}>
        <h3 style={{ margin: '0 0 8px 0', color: '#1890ff' }}>💡 提示</h3>
        <ul style={{ margin: 0, paddingLeft: '20px' }}>
          <li>打开浏览器开发者工具查看详细的控制台输出</li>
          <li>PageSpy的离线模式会将数据保存在本地，不会发送到服务器</li>
          <li>暂停状态会保存在localStorage中，页面刷新后会自动恢复</li>
          <li>如果DataHarbor的pause/resume方法不可用，系统会使用备用方案</li>
        </ul>
      </div>
    </div>
  );
};

export default PageSpyTestPage;
